/**
 * Utility functions for handling geolocation
 */

import { geocodingService } from '@/utils/geocoding';

// Interface for location data
export interface LocationData {
  address: string;
  coordinates?: [number, number]; // [latitude, longitude]
  source: 'geolocation' | 'profile' | 'default';
  accuracy?: 'high' | 'medium' | 'low';
  confidence?: number;
}



/**
 * Reverse geocode coordinates to get an address
 *
 * @param coordinates [latitude, longitude]
 * @returns Promise with the address string
 */
export const reverseGeocode = async (coordinates: [number, number]): Promise<string> => {
  try {
    const [latitude, longitude] = coordinates;
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1&countrycodes=ph`,
      {
        headers: {
          'User-Agent': 'RainbowPaws/1.0 (<EMAIL>)'
        }
      }
    );

    if (!response.ok) {
      throw new Error('Failed to reverse geocode coordinates');
    }

    const data = await response.json();

    // Format the address
    let address = '';

    if (data.address) {
      const parts = [];

      // Add city/town/village
      if (data.address.city) {
        parts.push(data.address.city);
      } else if (data.address.town) {
        parts.push(data.address.town);
      } else if (data.address.village) {
        parts.push(data.address.village);
      }

      // Add state/province
      if (data.address.state || data.address.province) {
        parts.push(data.address.state || data.address.province);
      }

      // Add country
      if (data.address.country) {
        parts.push(data.address.country);
      }

      address = parts.join(', ');
    }

    // If we couldn't parse the address, use the display_name
    if (!address && data.display_name) {
      address = data.display_name;
    }

    return address || 'Unknown location';
  } catch {
    return 'Unknown location';
  }
};

/**
 * Geocode an address to coordinates using the enhanced geocoding service
 *
 * @param address Address to geocode
 * @returns Promise with location data including coordinates and accuracy
 */
export const geocodeAddress = async (address: string): Promise<LocationData> => {
  try {
    const result = await geocodingService.geocodeAddress(address);

    return {
      address: result.formattedAddress,
      coordinates: result.coordinates,
      source: 'profile',
      accuracy: result.accuracy,
      confidence: result.confidence
    };
  } catch (error) {
    console.error('Geocoding error:', error);

    // Return default location with low confidence
    return {
      address: 'Balanga City, Bataan, Philippines (Default Location)',
      coordinates: [14.6742, 120.5434],
      source: 'default',
      accuracy: 'low',
      confidence: 0.1
    };
  }
};


