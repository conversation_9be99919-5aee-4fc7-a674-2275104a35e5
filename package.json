{"name": "app_rainbowpaws", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:unused": "eslint src/", "lint:unused:fix": "eslint src/ --fix", "type-check": "tsc --noEmit", "clean": "rimraf .next tsconfig.tsbuildinfo", "clean:all": "rimraf .next tsconfig.tsbuildinfo node_modules package-lock.json && npm install", "spring-clean": "npm run clean && npm run lint:fix && npm run type-check"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.10.4", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "mysql2": "^3.14.1", "next": "15.3.2", "node-cache": "^5.1.2", "nodemailer": "^7.0.3", "react": "^19", "react-dom": "^19", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0", "twilio": "^5.6.1", "zod": "^3.25.51"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.2", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.17", "@types/node": "22.15.17", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.4", "@types/react-dom": "^19.1.5", "@types/twilio": "^3.19.2", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.5.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}}